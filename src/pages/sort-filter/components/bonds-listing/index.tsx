import { useEffect, useRef, useCallback } from "react";
import type {
  BondItem,
  EmptyState,
  FilterItem,
  FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import BondsCardShimmer from "./shimmer";
import Anchor from "@/components/functional/anchor";
import { trackEvent } from "@/utils/analytics";
import { navigate } from "@/utils/routing";
import ProgressiveFilterRenderer from "./progressive-filter-renderer";
import type { AppliedFilter, FilterOptionValueWithLabel } from "../../machine";
import HorizontalBondsCard from "@/components/bonds/horizontal-bond-card";
import "./index.css";
import LinkButton from "@/components/ui/button/link-button";
import { supportLink } from "@/config/support";
import Chip from "@/components/ui/chip/chip";

interface BondsListingProps {
  listData?: BondItem[];
  onPageChange?: (page: number) => void;
  currentPage?: number;
  totalPages?: number;
  isLoading?: boolean;
  isFilterLoading?: boolean;
  suggestedFilters?: { [key: number]: FilterItem };
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
  emptyState?: EmptyState;
}

const BondsLisiting = ({
  listData,
  onPageChange,
  currentPage = 1,
  totalPages = 1,
  isLoading = false,
  isFilterLoading = false,
  suggestedFilters,
  handleFilterChange,
  appliedFilters,
  emptyState,
}: BondsListingProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasMorePages = currentPage < totalPages;
  const isEmptyState = emptyState && emptyState.alternateItems.length;
  if (isEmptyState) {
    listData = emptyState.alternateItems;
  }

  const handleScroll = useCallback(() => {
    if (!containerRef.current || !onPageChange || !hasMorePages || isLoading) {
      return;
    }

    if (throttleTimeoutRef.current) {
      clearTimeout(throttleTimeoutRef.current);
    }

    throttleTimeoutRef.current = setTimeout(() => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const containerBottom = containerRect.bottom;
      const windowHeight = window.innerHeight;

      if (containerBottom <= windowHeight + 100) {
        const nextPage = currentPage + 1;
        onPageChange(nextPage);
      }
    }, 100);
  }, [onPageChange, currentPage, hasMorePages, isLoading]);

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);

      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }
    };
  }, [handleScroll]);

  if (isFilterLoading) {
    return (
      <div
        className="space-y-4 overflow-y-auto px-5 pt-0 pb-5"
        style={{
          maxHeight: "calc(100vh - 220px)",
        }}
      >
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex flex-col space-y-4">
            <BondsCardShimmer />
            <hr className="ml-18 border-t-[0.5px]" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      {isEmptyState && (
        <div className="space-y-4 overflow-y-auto bg-white pt-0">
          <div className="flex flex-col space-y-4">
            <p className="text-heading4 text-black-80">
              {emptyState.title ??
                "Try changing filters or explore these <br /> curated bond collections below."}
            </p>
          </div>
          <div className="m-0 flex flex-wrap gap-3 px-5">
            {emptyState.suggestedFilters.map((option, index) => (
              <Chip
                key={`${option.key}-${index}`}
                selected={appliedFilters.some(
                  (filter) =>
                    filter.key === emptyState.suggestedFilters[index].key &&
                    filter.filters.includes(
                      option.options[0]
                        .optionValue as FilterOptionValueWithLabel
                    )
                )}
                size="medium"
                variant="outlined"
                onClick={() => {
                  handleFilterChange(
                    {
                      key: emptyState.suggestedFilters[index].key,
                      filters: option.options[0].optionValue!,
                      label: option.label,
                    },
                    !option.options[0].isSelected,
                    index
                  );
                }}
                className="text-body1 border-1 bg-white"
              >
                {option.label}
              </Chip>
            ))}
          </div>
          <hr className="my-6 border-t-[0.5px] px-5" />
        </div>
      )}
      <div
        ref={containerRef}
        className="floating-footer-padding space-y-4 bg-white pt-0 pb-5"
      >
        {listData?.map((item, index) => {
          return (
            <div key={index} className="count flex flex-col space-y-4">
              <Anchor
                href={`/bonds/${item.slug ?? "unknown"}/${item.isin}/calculator`}
                onClick={(event) => {
                  navigate(
                    `/bonds/${item.slug ?? "unknown"}/${item.isin}/calculator`,
                    event.nativeEvent
                  );
                  trackEvent("bonds_search_card_clicked", {
                    bond_isin: item.isin,
                    bond_name: item.displayTitle,
                    bond_type: item.investabilityStatus,
                    bond_xirr: item.ytm,
                  });
                }}
                className="space-x-1"
              >
                <HorizontalBondsCard item={item} />
              </Anchor>

              {suggestedFilters && suggestedFilters[index] ? (
                <div className="bg-purple/3 mt-2 mb-2 border-[0.5px] border-[rgba(0,0,0,0.10)] p-5">
                  <div className="flex flex-wrap gap-2">
                    <ProgressiveFilterRenderer
                      item={suggestedFilters[index]}
                      handleFilter={(filter, isSelected, selectedIndex) =>
                        handleFilterChange(
                          {
                            key: suggestedFilters[index].key,
                            filters: filter,
                            label:
                              suggestedFilters[index].options[selectedIndex]
                                .label,
                          },
                          isSelected,
                          selectedIndex
                        )
                      }
                      appliedFilters={appliedFilters}
                    />
                  </div>
                </div>
              ) : (
                <hr className="ml-19 border-t-[0.5px]" />
              )}
            </div>
          );
        })}
        {isLoading && !isFilterLoading && listData?.length && (
          <>
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={`shimmer-${index}`} className="flex flex-col space-y-4">
                <BondsCardShimmer />
                <hr className="ml-16 border-t-[0.5px]" />
              </div>
            ))}
          </>
        )}
        {currentPage === totalPages && (listData?.length ?? 0) > 0 && (
          <LinkButton href={supportLink}>
            <img
              src="https://assets.stablemoney.in/web-frontend/calling_card_june_18_v7.webp"
              alt=""
            />
          </LinkButton>
        )}
      </div>
    </>
  );
};

export default BondsLisiting;
